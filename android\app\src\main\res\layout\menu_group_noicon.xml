<?xml version="1.0" encoding="UTF-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/menu_item"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="horizontal">

    <TextView
        android:id="@+id/menu_item_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingTop="13dp"
        android:paddingBottom="13dp"
        android:paddingStart="22dp"
        android:paddingEnd="22dp"
        android:fontFamily="sans-serif-medium"
        android:textColor="?android:textColorPrimary"
        android:layout_centerVertical="true"/>

    <ImageView
        android:id="@+id/menu_group_indicator"
        android:layout_width="@dimen/sidebar_expand_indicator_size"
        android:layout_height="@dimen/sidebar_expand_indicator_size"
        android:layout_centerVertical="true"
        android:layout_alignParentEnd="true"
        android:paddingEnd="10dp" />

</RelativeLayout>