name: firebase-testing
on: [push, workflow_dispatch]
jobs:
  test-app:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Set up gcloud Cloud SDK environment
        # You may pin to the exact commit or the version.
        # uses: google-github-actions/setup-gcloud@94337306dda8180d967a56932ceb4ddcf01edae7
        uses: google-github-actions/setup-gcloud@v0.2.0
        with:
          # Version of the gcloud SDK to install. If unspecified or set to "latest", the latest available gcloud SDK version for the target platform will be installed. Example: "290.0.1".
          version: latest
          # <AUTHOR> <EMAIL>.iam.gserviceaccount.com.

          # Service account key to use for authentication. This should be the JSON formatted private key which can be exported from the Cloud Console. The value can be raw or base64-encoded.
          service_account_key: ${{ secrets.GCLOUD_KEY }}
          # ID of the Google Cloud project. If provided, this will configure gcloud to use this project ID by default for commands. Individual commands can still override the project using the --project flag which takes precedence.
          project_id: gn-test-firebase-test-lab
          # Export the provided credentials as Google Default Application Credentials. This will make the credentials available to later steps via the GOOGLE_APPLICATION_CREDENTIALS environment variable. Future steps that consume Default Application Credentials will automatically detect and use these credentials.
          export_default_credentials: true

      - name: Make gradlew executable
        run: chmod +x ./gradlew

      - name: Copying the appConfig file from androidTest to main
        run: cp -f ./app/src/androidTest/assets/appConfig.json ./app/src/main/assets/appConfig.json
        
      - name: Add dependencies in app/build.gradle
        run: sed -i "s/dependencies *\n*{/dependencies {\nandroidTestImplementation 'androidx.test.ext:junit:1.1.2'\nandroidTestImplementation 'androidx.test.espresso:espresso-core:3.3.0'\nandroidTestImplementation 'androidx.test.espresso:espresso-contrib:3.3.0'\nandroidTestImplementation 'androidx.test.espresso:espresso-web:3.3.0'\nandroidTestImplementation 'androidx.test.uiautomator:uiautomator:2.2.0'\n/" ./app/build.gradle

      - name: Add test runner in defaultConfig in app/build.gradle
        run: sed -i "s/defaultConfig *\n*{/defaultConfig {\ntestInstrumentationRunner 'androidx.test.runner.AndroidJUnitRunner'/" ./app/build.gradle
      
      - name: Adding Helper method in GoNativeWebviewClient.java
        run: sed -i "s/super.onPageFinished *( *view *, *url *) *;/super.onPageFinished(view, url);\nHelperClass.newLoad++;/" ./app/src/normal/java/io/gonative/android/GoNativeWebviewClient.java
      
      - name: Copying HelperClass.java from androidTest/assets to normal/java/io/gonative/android/
        run: cp -f ./app/src/androidTest/assets/HelperClass.java ./app/src/normal/java/io/gonative/android/HelperClass.java

      - name: Build the App
        run: ./gradlew assembleDebug assembleAndroidTest
    
      - name: Testing the App
        run: gcloud firebase test android run --type instrumentation --app ./app/build/outputs/apk/normal/debug/app-normal-debug.apk --test ./app/build/outputs/apk/androidTest/normal/debug/app-normal-debug-androidTest.apk --device model=flo,version=21,locale=en,orientation=portrait --device model=hammerhead,version=23,locale=en,orientation=portrait --device model=griffin,version=24,locale=en,orientation=portrait --device model=G8142,version=25,locale=en,orientation=portrait --device model=star2qlteue,version=26,locale=en,orientation=portrait --device model=walleye,version=27,locale=en,orientation=portrait --device model=OnePlus5T,version=28,locale=en,orientation=portrait --device model=x1q,version=29,locale=en,orientation=portrait --device model=flame,version=30,locale=en,orientation=portrait --results-bucket cloud-test-gn-test-firebase-test-lab --timeout 300s
