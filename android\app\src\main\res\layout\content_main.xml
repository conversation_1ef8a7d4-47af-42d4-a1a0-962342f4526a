<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    app:layout_behavior="@string/appbar_scrolling_view_behavior"
    tools:showIn="@layout/app_bar_main"
    android:layout_height="match_parent">

    <co.median.android.MySwipeRefreshLayout
        android:id="@+id/swipe_refresh"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@id/pluginView">

        <co.median.android.widget.SwipeHistoryNavigationLayout
            android:id="@+id/swipe_history_nav"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <RelativeLayout
                android:layout_width="fill_parent"
                android:layout_height="fill_parent">

                <co.median.android.widget.WebViewContainerView
                    android:id="@+id/webviewContainer"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent" />

                <View
                    android:id="@+id/webviewOverlay"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:alpha="0.0"
                    android:background="?android:attr/colorBackground" />

            </RelativeLayout>
        </co.median.android.widget.SwipeHistoryNavigationLayout>
    </co.median.android.MySwipeRefreshLayout>

    <RelativeLayout
        android:id="@+id/pluginView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_above="@id/bottom_navigation"
        android:layout_alignWithParentIfMissing="true"
        android:background="?android:attr/colorBackground"
        android:visibility="gone"/>

    <co.median.android.widget.MedianProgressView
        android:id="@+id/progress"
        android:background="@android:color/transparent"
        android:layout_width="@dimen/progress_indicator_size"
        android:layout_height="@dimen/progress_indicator_size"
        android:layout_centerInParent="true"
        android:visibility="invisible" />

    <com.google.android.material.bottomnavigation.BottomNavigationView
        android:id="@+id/bottom_navigation"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:elevation="8dp"
        android:visibility="gone"
        android:background="@color/tabBarBackground"
        style="@style/Widget.App.BottomNavigationView"
        app:itemActiveIndicatorStyle="@style/Widget.App.BottomNavigationView.ActiveIndicator"
        app:labelVisibilityMode="labeled"
        app:itemTextAppearance="@style/BottomNavTextStyle"
        app:itemTextAppearanceActive="@style/BottomNavTextStyle"
        app:itemTextAppearanceInactive="@style/BottomNavTextStyle"
        android:tag="bottom_navigation"/>

    <RelativeLayout
        android:id="@+id/fullscreen"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="?android:attr/colorBackground"
        android:layout_above="@+id/bottom_navigation"
        android:visibility="invisible" />

</RelativeLayout>