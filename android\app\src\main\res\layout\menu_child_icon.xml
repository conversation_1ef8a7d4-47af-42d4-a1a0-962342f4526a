<?xml version="1.0" encoding="UTF-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/menu_item"
    android:paddingBottom="5dp"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="horizontal">


    <ImageView
        android:id="@+id/menu_item_icon"
        android:layout_width="@dimen/sidebar_icon_size"
        android:layout_height="@dimen/sidebar_icon_size"
        android:layout_centerVertical="true"
        android:layout_marginStart="30dp"
        android:layout_alignParentStart="true" />

    <TextView
        android:id="@+id/menu_item_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="10dp"
        android:paddingTop="13dp"
        android:paddingBottom="13dp"
        android:paddingStart="5dp"
        android:paddingEnd="5dp"
        android:fontFamily="sans-serif-medium"
        android:textColor="?android:textColorPrimary"
        android:layout_centerVertical="true"
        android:layout_toEndOf="@id/menu_item_icon"/>

</RelativeLayout>