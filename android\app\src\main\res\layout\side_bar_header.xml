<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <ImageView
        android:id="@+id/app_logo"
        android:layout_width="80dp"
        android:layout_height="80dp"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="30dp"
        android:contentDescription="@string/app_logo"
        android:src="@mipmap/ic_sidebar_logo" />

    <TextView
        android:id="@+id/app_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/app_logo"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="20dp"
        android:layout_marginBottom="15dp"
        android:fontFamily="sans-serif-medium"
        android:textColor="@color/sidebarForeground"
        android:textSize="22sp" />

    <View
        android:id="@+id/header_divider"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_below="@id/app_name"
        android:background="@color/sidebarSeparatorColor" />
</RelativeLayout>