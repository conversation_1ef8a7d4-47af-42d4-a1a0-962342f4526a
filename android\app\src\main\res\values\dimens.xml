<resources>

    <!-- Default screen margins, per the Android Design guidelines. -->
    <dimen name="activity_horizontal_margin">16dp</dimen>
    <dimen name="activity_vertical_margin">16dp</dimen>
    <dimen name="sidebar_icon_size">22dp</dimen>
    <dimen name="sidebar_expand_indicator_size">22dp</dimen>
    <dimen name="sidebar_icon_width">25dp</dimen>
    <dimen name="bottom_navigation_margin_bottom">7dp</dimen>
    <dimen name="bottom_navigation_margin_top_active">7dp</dimen>
    <dimen name="bottom_navigation_margin_top_inactive">7dp</dimen>

    <dimen name="handle_icon_corner_radius">32dp</dimen>
    <dimen name="handle_icon_size">38dp</dimen>
    <dimen name="handle_icon_padding">8dp</dimen>
    <dimen name="handle_icon_elevation">2dp</dimen>
    <dimen name="handle_text_padding_right">12dp</dimen>
    <dimen name="handle_text_padding_bottom">1dp</dimen>

    <dimen name="progress_indicator_size">64dp</dimen>

    <dimen name="action_menu_icon_size">18dp</dimen>
    <dimen name="action_menu_icon_margin">46dp</dimen>
    <dimen name="action_menu_nav_offset">8dp</dimen>
    <dimen name="action_custom_menu_size">20dp</dimen>
    <dimen name="action_custom_menu_side_margin">12dp</dimen>
    <dimen name="action_custom_overflow_menu_width">170dp</dimen>>
</resources>
