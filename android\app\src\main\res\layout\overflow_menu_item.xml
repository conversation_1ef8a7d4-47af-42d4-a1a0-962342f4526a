<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/overFlowItemLayout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:padding="10dp"
    tools:ignore="UseCompoundDrawables">

    <ImageView
        android:id="@+id/overFlowItemDrawable"
        android:layout_width="@dimen/action_custom_menu_size"
        android:layout_height="@dimen/action_custom_menu_size"
        android:layout_gravity="center_vertical"
        android:layout_marginEnd="16dp"
        android:scaleType="centerInside" />

    <TextView
        android:id="@+id/overFlowItemTextView"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:textSize="16sp"
        android:textColor="@color/titleTextColor"
        android:layout_gravity="center_vertical"
        android:gravity="center_vertical" />
</LinearLayout>