{"general": {"screenOrientation": {"iphone": null, "ipad": null, "androidPhone": null, "androidTablet": null}, "userAgentRegexes": [], "replaceStrings": [], "nativeBridgeUrls": [], "languages": [], "userAgentAdd": "median", "enableWindowOpen": true, "forceUserAgent": "", "publicKey": "wwwomo", "deviceRegKey": "akammwj4u4u92qw2k6v4hatndw", "appName": "SnapEat – Where Speed Meets Co", "initialUrl": "https://griepit.in/", "androidPackageName": "co.median.android.wwwomo", "keepScreenOn": null, "iosUserAgentAdd": "median", "iosForceUserAgent": "", "androidUserAgentAdd": "median", "androidForceUserAgent": "", "iosBundleId": "co.median.ios.wwwomo", "androidSigningKey": "upload", "injectMedianJS": true, "forceSessionCookieExpiry": 0, "iosCustomHeaders": {}, "androidCustomHeaders": {}, "version": 4}, "navigation": {"tabNavigation": {"tabSelectionConfig": [], "tabMenus": [], "active": false}, "sidebarNavigation": {"menuSelectionConfig": {"redirectLocations": [{"regex": ".*", "menuName": "default", "loggedIn": true}]}, "menus": [{"active": false, "items": [{"url": "https://griepit.in/#", "label": "", "subLinks": []}], "name": "default"}]}, "regexInternalExternal": {"rules": [{"mode": "external", "label": "Non-web links", "pagesToTrigger": "custom", "regex": "^(?!https?://).*"}, {"mode": "appbrowser", "label": "Facebook", "pagesToTrigger": "custom", "regex": "https?://([-\\w]+\\.)*facebook\\.com.*"}, {"mode": "appbrowser", "label": "Twitter/X", "pagesToTrigger": "custom", "regex": "https?://([\\-\\w]+\\.)*(twitter|x)\\.com/.*"}, {"mode": "appbrowser", "label": "Instagram", "pagesToTrigger": "custom", "regex": "https?://([-\\w]+\\.)*instagram\\.com/.*"}, {"mode": "appbrowser", "label": "Google Maps", "pagesToTrigger": "custom", "regex": "https?://maps\\.google\\.com.*"}, {"mode": "appbrowser", "label": "Google Maps Search", "pagesToTrigger": "custom", "regex": "https?://([-\\w]+\\.)*google\\.com/maps/search/.*"}, {"mode": "appbrowser", "label": "LinkedIn", "pagesToTrigger": "custom", "regex": "https?://([-\\w]+\\.)*linkedin\\.com/.*"}, {"mode": "internal", "label": "Microsoft Login", "pagesToTrigger": "custom", "regex": "https?://login\\.microsoftonline\\.com.*"}, {"mode": "internal", "label": "All pages on my domain", "pagesToTrigger": "domain", "regex": "https?:\\/\\/([-\\w]+\\.)*griepit.in(\\/.*)?$"}, {"mode": "appbrowser", "label": "All Other Links", "pagesToTrigger": "all", "regex": ".*"}], "active": true}, "androidPullToRefresh": false, "iosPullToRefresh": false, "navigationTitles": {"titles": [], "active": true}, "ignorePageFinishedRegexes": [], "actionConfig": {"actions": [], "actionSelection": [], "active": true}, "androidShowRefreshButton": false, "iosShowRefreshButton": false, "deepLinkDomains": {"domains": [], "enableAndroidApplinks": false}, "navigationLevels": {"levels": [], "active": true}, "toolbarNavigation": {"enabled": false, "visibilityByPages": "allPages", "visibilityByBackButton": "backButtonActive", "regexes": [{"enabled": true, "regex": ".*"}], "items": [{"system": "back", "titleType": "defaultText", "visibility": "allPages", "urlRegex": [{"enabled": true, "regex": ".*"}]}, {"system": "refresh", "enabled": false, "visibility": "allPages", "urlRegex": [{"enabled": true, "regex": ".*"}]}, {"system": "forward", "enabled": false, "titleType": "defaultText", "visibility": "allPages", "urlRegex": [{"enabled": true, "regex": ".*"}]}]}, "redirects": [], "iosShowOfflinePage": true, "iosConnectionOfflineTime": 10, "androidShowOfflinePage": true, "androidConnectionOfflineTime": 10, "iosSettings": {}, "swipeGestures": true, "maxWindows": {"enabled": false, "numWindows": 5, "autoClose": false}, "iosOfflinePageUrl": "https://s3.amazonaws.com/gonativeio/app_files/wwwomo/ios_offline_html_1744567985177.html"}, "styling": {"splashScreen": {"ios": {"animationLoop": "once", "backgroundColor": "#FFFFFF", "backgroundColorDark": "#000000", "backgroundImage": "https://s3.amazonaws.com/gonativeio/app_files/wwwomo/1744565955500_apw0d18630e4g_FFFFFF_1_2x2.png", "backgroundImageDark": "https://s3.amazonaws.com/gonativeio/app_files/wwwomo/1744565955524_jge2r4vw9kyzj_000000_1_2x2.png", "animated": false, "centerImage": "https://s3.amazonaws.com/gonativeio/app_files/wwwomo/app_icon_m5kk7m5mch4v8_1744565923.png", "centerImageDark": "https://s3.amazonaws.com/gonativeio/app_files/wwwomo/app_icon_m5kk7m5mch4v8_1744565923.png", "animation": null, "animationDark": null, "animationFrame": null, "animationFrameDark": null}, "android": {"launchImages": {"mdpi": "https://s3.amazonaws.com/gonativeio/app_files/wwwomo/1744565955717_eycyf6jjpwy8e_180x180.png", "hdpi": "https://s3.amazonaws.com/gonativeio/app_files/wwwomo/1744565955741_df4rbgev5zftg_270x270.png", "xhdpi": "https://s3.amazonaws.com/gonativeio/app_files/wwwomo/1744565955763_k4gxjq9qmmybw_360x360.png", "xxhdpi": "https://s3.amazonaws.com/gonativeio/app_files/wwwomo/1744565955796_1wd25rfawk87u_540x540.png", "xxxhdpi": "https://s3.amazonaws.com/gonativeio/app_files/wwwomo/1744565955820_8ku5ut8n8v87p_720x720.png"}, "launchImagesDark": {"mdpi": "https://s3.amazonaws.com/gonativeio/app_files/wwwomo/1744565955875_mycrkt083z4kw_180x180.png", "hdpi": "https://s3.amazonaws.com/gonativeio/app_files/wwwomo/1744565955929_r1jp35mkqrmwm_270x270.png", "xhdpi": "https://s3.amazonaws.com/gonativeio/app_files/wwwomo/1744565955943_r94rxjg8cjdwu_360x360.png", "xxhdpi": "https://s3.amazonaws.com/gonativeio/app_files/wwwomo/1744565955982_anng8ykq0h1ku_540x540.png", "xxxhdpi": "https://s3.amazonaws.com/gonativeio/app_files/wwwomo/1744565956039_nmudbm82efz78_720x720.png"}, "showSplash": true, "animationLoop": "once", "backgroundColor": "#FFFFFF", "backgroundColorDark": "#000000", "animated": false, "animation": null, "animationDark": null, "animationFrame": null, "animationFrameDark": null}}, "transitionInteractiveDelayMax": 0.2, "menuAnimationDuration": 0.15, "disableAnimations": false, "hideWebviewAlpha": 0.5, "iosDarkMode": "auto", "iosTitleColor": "#0E0D08", "iosTitleColorDark": "#ffffff", "iosTintColor": "#0E0D08", "iosTintColorDark": "#ffffff", "iosSidebarTextColor": "#0E0D08", "iosSidebarTextColorDark": "#ffffff", "iosStatusBarBackgroundColor": "#ffffffff", "iosStatusBarBackgroundColorDark": "#000000", "iosEnableBlurInStatusBar": false, "iosSidebarBackgroundColor": "#f8f8f8", "iosSidebarBackgroundColorDark": "#202020", "iosNavigationBarTintColor": "#f8f8f8", "iosNavigationBarTintColorDark": "#202020", "iosTabBarTintColor": "#f8f8f8", "iosTabBarTintColorDark": "#000000", "iosTabBarInactiveColor": "#A1A1A1", "iosTabBarInactiveColorDark": "#818181", "icon": "https://s3.amazonaws.com/gonativeio/app_files/wwwomo/app_icon_m5kk7m5mch4v8_1744565923.png", "iosHeaderImage": "https://s3.amazonaws.com/gonativeio/app_files/wwwomo/app_icon_m5kk7m5mch4v8_1744565923.png", "showActionBar": false, "showNavigationBar": false, "iosSidebarFont": "<PERSON><PERSON><PERSON>", "androidHideTitleInActionBar": false, "navigationTitleImage": false, "iosTheme": "default", "androidTheme": "auto", "androidBackgroundColor": "#FFFFFF", "androidSidebarBackgroundColor": "#FFFFFF", "androidSidebarForegroundColor": "#1A100B", "androidActionBarBackgroundColor": "#FFFFFF", "androidActionBarForegroundColor": "#1A100B", "androidPullToRefreshColor": "#1A100B", "androidAccentColor": "#009688", "androidSidebarSeparatorColor": "#CCCCCC", "androidSidebarHighlightColor": "#1A100B", "androidTabBarBackgroundColor": "#FFFFFF", "androidTabBarTextColor": "#949494", "androidTabBarIndicatorColor": "#1A100B", "androidTabPressedBackgroundColor": "#CCCCCC", "androidStatusBarBackgroundColor": "#5C5C5C", "androidShowLogoInSideBar": true, "androidShowAppNameInSideBar": true, "androidSwipeNavigationBackgroundColor": "#FFFFFF", "androidSwipeNavigationActiveColor": "#000000", "androidSwipeNavigationInactiveColor": "#666666", "androidActionBarBackgroundColorDark": "#333333", "androidStatusBarBackgroundColorDark": "#333333", "androidActionBarForegroundColorDark": "#FFFFFF", "androidAccentColorDark": "#80cbc4", "androidBackgroundColorDark": "#333333", "androidSidebarForegroundColorDark": "#FFFFFF", "androidSidebarBackgroundColorDark": "#333333", "androidSidebarSeparatorColorDark": "#666666", "androidSidebarHighlightColorDark": "#FFFFFF", "androidPullToRefreshColorDark": "#FFFFFF", "androidTabBarTextColorDark": "#FFFFFF", "androidTabBarBackgroundColorDark": "#333333", "androidTabBarIndicatorColorDark": "#666666", "androidTabPressedBackgroundColorDark": "#999999", "androidSwipeNavigationBackgroundColorDark": "#333333", "androidSwipeNavigationActiveColorDark": "#FFFFFF", "androidSwipeNavigationInactiveColorDark": "#666666", "androidSystemNavBarColor": "#FFFFFF", "androidSystemNavBarColorDark": "#000000", "androidWebviewTextZoom": null, "androidIcon": "https://s3.amazonaws.com/gonativeio/app_files/wwwomo/app_icon_m5kk7m5mch4v8_1744565923.png", "iosIcon": "https://s3.amazonaws.com/gonativeio/app_files/wwwomo/app_icon_m5kk7m5mch4v8_1744565923.png", "customIcons": {}, "iosStatusBarStyle": "auto", "iosEnableOverlayInStatusBar": false, "androidStatusBarStyle": "auto", "androidEnableOverlayInStatusBar": false, "iosActivityIndicatorColor": "#808080", "iosActivityIndicatorColorDark": "#808080", "androidActivityIndicatorColor": "#808080", "androidActivityIndicatorColorDark": "#808080", "forceViewportWidth": null, "pinchToZoom": null, "iosDynamicType": true, "navigationTitleImageLocation": "assets/defaults/app-icon-placeholder.png", "navigationTitleImageLocationDark": "assets/defaults/app-icon-placeholder.png", "androidHeaderImage": "assets/defaults/app-icon-placeholder.png", "androidHeaderImageDark": "assets/defaults/app-icon-placeholder.png", "iosHeaderImageDark": "https://s3.amazonaws.com/gonativeio/app_files/wwwomo/app_icon_m5kk7m5mch4v8_1744565923.png"}, "permissions": {"usesGeolocation": true, "androidDownloadToPublicStorage": false, "enableWebRTCamera": false, "enableWebRTCMicrophone": false, "iosLocationUsageDescription": "SnapEat needs access to your location to customize your app experience.", "iosCameraUsageDescription": "SnapEat needs access to your camera to upload photos.", "iosMicrophoneUsageDescription": "SnapEat needs access to your microphone to capture audio.", "iOSATTUserTrackingDescription": "Your data will be used to personalize your experience and at times to deliver personalized ads", "iOSRequestATTConsentOnLoad": false}, "developmentTools": {"enableWebConsoleLogs": true}, "services": {"oneSignalV5": {"active": true}}, "contextMenu": {"linkActions": ["copyLink", "openExternal"], "enabled": false}, "allowZoom": null, "security": {"network": {"allowInsecure": null}}}