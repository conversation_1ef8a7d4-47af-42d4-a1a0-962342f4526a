<?xml version="1.0" encoding="utf-8"?>
<co.median.android.widget.GoNativeDrawerLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/drawer_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:openDrawer="start">

    <include
        android:id="@+id/app_bar_main"
        layout="@layout/app_bar_main"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <com.google.android.material.navigation.NavigationView
        android:id="@+id/nav_menu"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_gravity="start"
        android:fitsSystemWindows="true"
        android:background="@color/sidebarBackground">

        <RelativeLayout
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:paddingTop="11dp">

            <include layout="@layout/side_bar_header"
                android:id="@+id/header_layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="15dp"/>

            <ExpandableListView
                android:id="@+id/drawer_list"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_below="@id/header_layout"
                android:layout_marginStart="10dp"
                android:layout_marginLeft="10dp"
                android:layout_marginEnd="10dp"
                android:layout_marginRight="10dp"
                android:choiceMode="singleChoice"
                android:divider="@null"
                android:footerDividersEnabled="false"
                android:groupIndicator="@null"
                android:headerDividersEnabled="false"
                android:listSelector="@android:color/transparent"
                android:scrollbarStyle="insideOverlay"
                android:scrollbars="vertical" />
        </RelativeLayout>
    </com.google.android.material.navigation.NavigationView>
</co.median.android.widget.GoNativeDrawerLayout>