<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="Theme.Median" parent="Theme.Material3.DayNight.NoActionBar">
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="titleTextColor">@color/titleTextColor</item>
        <item name="colorAccent">@color/colorAccent</item>
        <item name="colorSecondary">@color/colorAccent</item>
        <item name="android:colorBackground">@color/colorBackground</item>
        <item name="materialAlertDialogTheme">@style/MaterialAlertDialogTheme</item>>
        <item name="alertDialogTheme">@style/AlertDialogTheme</item>
        <item name="android:alertDialogTheme">@style/LegacyAlertDialogTheme</item>
        <item name="android:datePickerDialogTheme">@style/DatePickerDialogTheme</item>
        <item name="android:timePickerDialogTheme">@style/DatePickerDialogTheme</item>
    </style>

    <style name="Theme.Median.AppBarOverlay" parent="ThemeOverlay.AppCompat.Dark.ActionBar" />

    <style name="Theme.Median.PopupOverlay" parent="ThemeOverlay.AppCompat.Light" />

    <style name="Widget.App.BottomNavigationView" parent="Widget.Material3.BottomNavigationView">
        <item name="itemIconTint">@color/bottom_bar_text_color</item>
        <item name="itemTextColor">@color/bottom_bar_text_color</item>
    </style>

    <!--
    In the Material 3 theme, the button text color in all types of AlertDialog defaults to the theme's primary color.
    To ensure better visibility, override the button text color to use the accent color.
    -->
    <style name="MaterialAlertDialogTheme" parent="ThemeOverlay.Material3.MaterialAlertDialog">
        <item name="buttonBarPositiveButtonStyle">@style/MyMaterialAlertDialogButtonStyle</item>
        <item name="buttonBarNegativeButtonStyle">@style/MyMaterialAlertDialogButtonStyle</item>
        <item name="buttonBarNeutralButtonStyle">@style/MyMaterialAlertDialogButtonStyle</item>
    </style>

    <!-- Apply the same override for the androidx AlertDialog. -->
    <style name="AlertDialogTheme" parent="ThemeOverlay.MaterialComponents.Dialog.Alert">
        <item name="buttonBarButtonStyle">@style/MyMaterialAlertDialogButtonStyle</item>
    </style>

    <style name="MyMaterialAlertDialogButtonStyle" parent="Widget.Material3.Button.TextButton.Dialog">
        <item name="android:textColor">@color/colorAccent</item>
    </style>

    <!-- Apply the same override for the legacy AlertDialog. -->
    <style name="LegacyAlertDialogTheme" parent="Theme.AppCompat.Light.Dialog.Alert">
        <item name="android:buttonBarButtonStyle">@style/MyAlertDialogButtonStyle</item>
    </style>

    <style name="MyAlertDialogButtonStyle" parent="Widget.AppCompat.Button.Borderless">
        <item name="android:textColor">@color/colorAccent</item>
    </style>

    <style name="Widget.App.BottomNavigationView.ActiveIndicator" parent="Widget.Material3.BottomNavigationView.ActiveIndicator">
        <item name="android:color">@color/bottom_bar_highlight_color</item>
    </style>

    <style name="DatePickerDialogTheme" parent="ThemeOverlay.Material3.Dialog">
        <item name="colorPrimary">@color/colorAccent</item>
        <item name="colorAccent">@color/colorAccent</item>
    </style>

    <style name="SplashTheme" parent="Theme.SplashScreen">
        <item name="windowSplashScreenBackground">@color/splash_background</item>
        <item name="windowSplashScreenAnimatedIcon">@drawable/splash</item>
        <item name="postSplashScreenTheme">@style/Theme.Median</item>
        <item name="android:windowLightStatusBar">true</item>
    </style>

    <attr name="ic_action_refresh" format="reference"/>
    <attr name="ic_action_search" format="reference"/>
    <attr name="ic_action_share" format="reference"/>

    <style name="LoginFormContainer">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:padding">16dp</item>
    </style>

    <attr name="inactiveColor" format="color" />
    <attr name="activeColor" format="color" />
    <attr name="handleBackground" format="reference" />

    <declare-styleable name="HandleView">
        <attr name="iconDrawable" format="reference" />
        <attr name="text" format="string" />
        <attr name="inactiveColor" />
        <attr name="activeColor" />
        <attr name="handleBackground" />
    </declare-styleable>

    <declare-styleable name="SwipeHistoryNavigationLayout">
        <attr name="leftHandleDrawable" format="reference" />
        <attr name="rightHandleDrawable" format="reference" />
        <attr name="handleBackground" />
        <attr name="leftHandleLabel" format="string" />
        <attr name="inactiveColor" />
        <attr name="activeColor" />
    </declare-styleable>

    <style name="BottomNavTextStyle" parent="TextAppearance.AppCompat">
        <item name="android:textSize">12sp</item>
        <item name="android:textStyle">normal</item>
    </style>
</resources>
