// Top-level build file where you can add configuration options common to all sub-projects/modules.
buildscript {
    ext {
        kotlin_version = '2.1.20'
        coreVersion = '2.7.5'
        iconsVersion = '1.3.1'
    }
    repositories {
        google()
        mavenCentral()
        gradlePluginPortal()
        maven {
            url "https://plugins.gradle.org/m2/"
        }
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:8.8.0'
        classpath 'gradle.plugin.com.onesignal:onesignal-gradle-plugin:0.14.0'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        //[enabled by builder] classpath 'com.google.gms:google-services:4.3.13'
        //[enabled by builder] classpath 'com.google.firebase:firebase-crashlytics-gradle:2.9.7'
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
        maven { url 'https://maven.median.co' }
        maven { url("$rootDir/maven") }
    }

    configurations.configureEach {
        resolutionStrategy.dependencySubstitution {
            substitute module('com.github.gonativeio:gonative-android-core') using module("co.median.android:core:$coreVersion")
        }
    }
}
