name: Android CI Build

on:
  pull_request:
    branches:
      - master

jobs:
  build:
    name: Android CI Build

    runs-on: ubuntu-latest
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Set up JDK 17
        uses: actions/setup-java@v4
        with:
          distribution: 'adopt'
          java-version: 17

      - name: Cache Gradle dependencies
        uses: actions/cache@v4
        with:
          path: |
            ~/.gradle/caches
            ~/.gradle/wrapper
          key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle', '**/gradle.lockfile') }}
          restore-keys: |
            ${{ runner.os }}-gradle-

      - name: Make Gradle Wrapper Executable
        run: chmod +x ./gradlew

      - name: Build Android App
        run: ./gradlew assembleDebug

      - name: Upload APK Artifact
        uses: actions/upload-artifact@v4
        with:
          name: app-debug-apk
          path: app/build/outputs/apk/debug/app-normal-debug.apk
